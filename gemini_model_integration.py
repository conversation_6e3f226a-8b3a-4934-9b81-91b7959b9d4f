#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gemini模型集成
用于处理复杂查询和生成知识模式
"""

import json
import time
import asyncio
import re
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class GeminiResponse:
    """Gemini响应数据类"""
    success: bool
    content: str
    usage: Dict[str, Any]
    response_time: float
    model_version: str
    error: Optional[str] = None

class GeminiModelClient:
    """Gemini模型客户端"""
    
    def __init__(self, api_key: str = None, model_name: str = "gemini-pro"):
        """
        初始化Gemini客户端
        
        Args:
            api_key: Google API密钥
            model_name: 模型名称
        """
        self.api_key = api_key
        self.model_name = model_name
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        
        # 初始化客户端
        self._init_client()
        
        # 统计信息
        self.total_requests = 0
        self.successful_requests = 0
        self.total_tokens = 0
        self.total_cost = 0.0
        
    def _init_client(self):
        """初始化客户端"""
        try:
            import google.generativeai as genai
            
            if self.api_key:
                genai.configure(api_key=self.api_key)
                self.client = genai.GenerativeModel(self.model_name)
                self.available = True
                logger.info(f"Gemini客户端初始化成功: {self.model_name}")
            else:
                logger.warning("未提供Gemini API密钥，使用模拟模式")
                self.client = None
                self.available = False
                
        except ImportError:
            logger.warning("未安装google-generativeai包，使用模拟模式")
            self.client = None
            self.available = False
        except Exception as e:
            logger.error(f"Gemini客户端初始化失败: {e}")
            self.client = None
            self.available = False
    
    async def generate_knowledge_pattern(self, query: str, context: Dict[str, Any] = None) -> GeminiResponse:
        """
        生成知识模式
        
        Args:
            query: 用户查询
            context: 上下文信息
            
        Returns:
            GeminiResponse: 生成的知识模式
        """
        start_time = time.time()
        self.total_requests += 1
        
        try:
            # 构建提示词
            prompt = self._build_knowledge_pattern_prompt(query, context)
            
            if self.available and self.client:
                # 调用真实的Gemini API
                response = await self._call_gemini_api(prompt)
            else:
                # 使用模拟响应
                response = self._mock_gemini_response(prompt)
            
            response_time = time.time() - start_time
            
            if response.success:
                self.successful_requests += 1
                self.total_tokens += response.usage.get("total_tokens", 0)
                
                # 解析生成的知识模式
                knowledge_pattern = self._parse_knowledge_pattern(response.content)
                response.content = knowledge_pattern
            
            response.response_time = response_time
            return response
            
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"生成知识模式失败: {e}")
            
            return GeminiResponse(
                success=False,
                content="",
                usage={},
                response_time=response_time,
                model_version=self.model_name,
                error=str(e)
            )
    
    async def analyze_complex_query(self, query: str, context: Dict[str, Any] = None) -> GeminiResponse:
        """
        分析复杂查询
        
        Args:
            query: 复杂查询
            context: 上下文信息
            
        Returns:
            GeminiResponse: 分析结果
        """
        start_time = time.time()
        self.total_requests += 1
        
        try:
            # 构建分析提示词
            prompt = self._build_analysis_prompt(query, context)
            
            if self.available and self.client:
                response = await self._call_gemini_api(prompt)
            else:
                response = self._mock_analysis_response(query, context)
            
            response_time = time.time() - start_time
            
            if response.success:
                self.successful_requests += 1
                self.total_tokens += response.usage.get("total_tokens", 0)
            
            response.response_time = response_time
            return response
            
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"分析复杂查询失败: {e}")
            
            return GeminiResponse(
                success=False,
                content="",
                usage={},
                response_time=response_time,
                model_version=self.model_name,
                error=str(e)
            )
    
    def _build_knowledge_pattern_prompt(self, query: str, context: Dict[str, Any] = None) -> str:
        """构建知识模式生成提示词"""
        prompt = f"""
你是一个专业的网络统计分析专家，需要为MCP服务器创建智能查询模式。

用户查询: {query}

请分析这个查询并生成一个结构化的知识模式，包含以下信息：

1. 查询意图分类
2. 关键参数提取规则
3. API调用参数映射
4. 相似查询示例
5. 错误处理建议

请以JSON格式返回结果：
{{
    "intent_type": "查询类型",
    "description": "查询描述",
    "keywords": ["关键词列表"],
    "parameters": {{
        "table": "数据表名",
        "fields": ["字段列表"],
        "keys": ["键字段列表"],
        "extraction_rules": {{
            "netlink_id": "链路ID提取规则",
            "time_range": "时间范围提取规则",
            "topcount": "Top数量提取规则"
        }}
    }},
    "examples": ["相似查询示例"],
    "api_mapping": {{
        "endpoint": "API端点",
        "method": "调用方法",
        "required_params": ["必需参数"]
    }},
    "error_handling": {{
        "common_errors": ["常见错误"],
        "fallback_strategy": "降级策略"
    }}
}}

上下文信息: {json.dumps(context, ensure_ascii=False) if context else "无"}

请确保生成的模式具有通用性，能够处理类似的查询变体。
"""
        return prompt
    
    def _build_analysis_prompt(self, query: str, context: Dict[str, Any] = None) -> str:
        """构建复杂查询分析提示词"""
        prompt = f"""
你是一个网络流量分析专家，需要分析复杂的网络统计查询。

用户查询: {query}

请深入分析这个查询，提供以下信息：

1. 查询复杂度评估
2. 需要的数据源和表
3. 分析步骤分解
4. 可能的性能瓶颈
5. 优化建议
6. 结果解释方案

请以结构化的方式返回分析结果，包含具体的实施建议。

上下文信息: {json.dumps(context, ensure_ascii=False) if context else "无"}

请提供详细的技术分析和实用的建议。
"""
        return prompt
    
    async def _call_gemini_api(self, prompt: str) -> GeminiResponse:
        """调用Gemini API"""
        try:
            # 生成内容
            response = await self.client.generate_content(
                prompt,
                generation_config={
                    "temperature": 0.7,
                    "top_p": 0.8,
                    "top_k": 40,
                    "max_output_tokens": 2048,
                }
            )
            
            return GeminiResponse(
                success=True,
                content=response.text,
                usage={
                    "prompt_tokens": len(prompt.split()),
                    "completion_tokens": len(response.text.split()),
                    "total_tokens": len(prompt.split()) + len(response.text.split())
                },
                response_time=0,  # 将在调用方设置
                model_version=self.model_name
            )
            
        except Exception as e:
            logger.error(f"Gemini API调用失败: {e}")
            return GeminiResponse(
                success=False,
                content="",
                usage={},
                response_time=0,
                model_version=self.model_name,
                error=str(e)
            )
    
    def _mock_gemini_response(self, prompt: str) -> GeminiResponse:
        """模拟Gemini响应（用于测试）"""
        # 简单的模拟响应
        mock_pattern = {
            "intent_type": "stats_query",
            "description": "网络统计查询",
            "keywords": ["统计", "流量", "链路"],
            "parameters": {
                "table": "session_stat",
                "fields": ["total_byte", "total_packet"],
                "keys": ["server_ip_addr"],
                "extraction_rules": {
                    "netlink_id": "从'链路X'中提取数字X",
                    "time_range": "识别时间词汇如'今天'、'昨天'",
                    "topcount": "从'TopX'中提取数字X"
                }
            },
            "examples": [
                "查询链路1的统计数据",
                "统计今天的流量情况"
            ],
            "api_mapping": {
                "endpoint": "query_statistics_table",
                "method": "POST",
                "required_params": ["table", "begintime", "endtime"]
            },
            "error_handling": {
                "common_errors": ["参数缺失", "时间格式错误"],
                "fallback_strategy": "使用默认参数"
            }
        }
        
        return GeminiResponse(
            success=True,
            content=json.dumps(mock_pattern, ensure_ascii=False, indent=2),
            usage={
                "prompt_tokens": len(prompt.split()),
                "completion_tokens": 200,
                "total_tokens": len(prompt.split()) + 200
            },
            response_time=0,
            model_version=self.model_name + "_mock"
        )
    
    def _mock_analysis_response(self, query: str, context: Dict[str, Any] = None) -> GeminiResponse:
        """模拟分析响应"""
        analysis = f"""
# 查询分析报告

## 查询: {query}

### 复杂度评估
- 复杂度级别: 中等
- 预估处理时间: 2-5秒
- 资源需求: 中等

### 数据源需求
- 主表: session_stat
- 辅助表: 无
- 索引需求: server_ip_addr, time_range

### 分析步骤
1. 解析查询参数
2. 构建SQL查询
3. 执行数据检索
4. 结果聚合和排序
5. 格式化输出

### 性能优化建议
- 使用时间范围索引
- 限制返回记录数
- 考虑缓存策略

### 结果解释
查询将返回指定条件下的统计数据，包含流量、包数等关键指标。
"""
        
        return GeminiResponse(
            success=True,
            content=analysis,
            usage={
                "prompt_tokens": 100,
                "completion_tokens": 150,
                "total_tokens": 250
            },
            response_time=0,
            model_version=self.model_name + "_mock"
        )
    
    def _parse_knowledge_pattern(self, content: str) -> Dict[str, Any]:
        """解析生成的知识模式"""
        try:
            # 尝试提取JSON内容
            json_match = re.search(r'```json\s*\n(.*?)\n```', content, re.DOTALL)
            if json_match:
                json_content = json_match.group(1)
                return json.loads(json_content)
            
            # 尝试直接解析JSON
            if content.strip().startswith('{'):
                return json.loads(content)
            
            # 如果不是JSON格式，返回原始内容
            return {
                "raw_content": content,
                "parsed": False,
                "type": "text"
            }
            
        except json.JSONDecodeError:
            return {
                "raw_content": content,
                "parsed": False,
                "type": "text",
                "error": "JSON解析失败"
            }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取使用统计"""
        success_rate = self.successful_requests / self.total_requests if self.total_requests > 0 else 0
        
        return {
            "model_name": self.model_name,
            "available": self.available,
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "success_rate": success_rate,
            "total_tokens": self.total_tokens,
            "estimated_cost": self.total_cost,
            "average_tokens_per_request": self.total_tokens / self.total_requests if self.total_requests > 0 else 0
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.total_requests = 0
        self.successful_requests = 0
        self.total_tokens = 0
        self.total_cost = 0.0

# 全局Gemini客户端实例
gemini_client = None

def initialize_gemini_client(api_key: str = None, model_name: str = "gemini-pro") -> GeminiModelClient:
    """初始化全局Gemini客户端"""
    global gemini_client
    gemini_client = GeminiModelClient(api_key, model_name)
    return gemini_client

def get_gemini_client() -> Optional[GeminiModelClient]:
    """获取全局Gemini客户端"""
    return gemini_client

# 便捷函数
async def generate_knowledge_with_gemini(query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
    """使用Gemini生成知识模式"""
    client = get_gemini_client()
    if not client:
        return {
            "success": False,
            "error": "Gemini客户端未初始化"
        }
    
    response = await client.generate_knowledge_pattern(query, context)
    
    return {
        "success": response.success,
        "knowledge_pattern": response.content if response.success else None,
        "usage": response.usage,
        "response_time": response.response_time,
        "error": response.error
    }

async def analyze_complex_query_with_gemini(query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
    """使用Gemini分析复杂查询"""
    client = get_gemini_client()
    if not client:
        return {
            "success": False,
            "error": "Gemini客户端未初始化"
        }
    
    response = await client.analyze_complex_query(query, context)
    
    return {
        "success": response.success,
        "analysis": response.content if response.success else None,
        "usage": response.usage,
        "response_time": response.response_time,
        "error": response.error
    }
